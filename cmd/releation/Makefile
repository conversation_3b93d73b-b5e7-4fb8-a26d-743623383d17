# VLab 剧集关联关系处理工具 Makefile

# 变量定义
BINARY_NAME=releation
BUILD_DIR=../../bin
SOURCE_DIR=.
GO_FILES=$(wildcard *.go)

# 默认目标
.PHONY: all
all: build

# 编译目标
.PHONY: build
build: $(BUILD_DIR)/$(BINARY_NAME)

$(BUILD_DIR)/$(BINARY_NAME): $(GO_FILES)
	@echo "编译 $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(BINARY_NAME) $(SOURCE_DIR)/*.go
	@echo "编译完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 清理目标
.PHONY: clean
clean:
	@echo "清理编译文件..."
	@rm -f $(BUILD_DIR)/$(BINARY_NAME)
	@echo "清理完成"

# 安装目标（复制到系统路径）
.PHONY: install
install: build
	@echo "安装 $(BINARY_NAME) 到 /usr/local/bin/..."
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	@echo "安装完成"

# 卸载目标
.PHONY: uninstall
uninstall:
	@echo "卸载 $(BINARY_NAME)..."
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)
	@echo "卸载完成"

# 运行帮助
.PHONY: help
help: build
	@$(BUILD_DIR)/$(BINARY_NAME) --help

# 运行测试（dry-run模式）
.PHONY: test
test: build
	@echo "运行测试模式..."
	@$(BUILD_DIR)/$(BINARY_NAME) --dry-run --verbose --limit 1

# 检查代码格式
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	@go fmt ./...
	@echo "格式化完成"

# 代码检查
.PHONY: vet
vet:
	@echo "检查代码..."
	@go vet ./...
	@echo "检查完成"

# 显示版本信息
.PHONY: version
version: build
	@echo "$(BINARY_NAME) 版本信息:"
	@echo "构建时间: $(shell date)"
	@echo "Go版本: $(shell go version)"

# 显示使用说明
.PHONY: usage
usage:
	@echo "VLab 剧集关联关系处理工具 - 使用说明"
	@echo ""
	@echo "可用的 make 目标:"
	@echo "  build     - 编译程序"
	@echo "  clean     - 清理编译文件"
	@echo "  install   - 安装到系统路径"
	@echo "  uninstall - 从系统路径卸载"
	@echo "  help      - 显示程序帮助信息"
	@echo "  test      - 运行测试模式"
	@echo "  fmt       - 格式化代码"
	@echo "  vet       - 检查代码"
	@echo "  version   - 显示版本信息"
	@echo "  usage     - 显示此使用说明"
	@echo ""
	@echo "示例:"
	@echo "  make build                    # 编译程序"
	@echo "  make test                     # 运行测试"
	@echo "  make install                  # 安装到系统"
	@echo "  ../../bin/releation --help    # 查看程序帮助"
