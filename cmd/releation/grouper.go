package main

import (
	"fmt"
	"sort"

	show "vlab/app/dao/content_show"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
)

// ShowGrouper 分组处理器
type ShowGrouper struct {
	showRepo show.Repo
}

// NewShowGrouper 创建分组处理器
func NewShowGrouper() *ShowGrouper {
	return &ShowGrouper{
		showRepo: show.GetRepo(),
	}
}

// CreateRelationGroups 根据分析结果创建关联组
func (g *ShowGrouper) CreateRelationGroups(ctx *gin.Context, baseNameGroups []*BaseNameGroup) ([]*RelationGroupInfo, error) {
	var relationGroups []*RelationGroupInfo

	for _, baseGroup := range baseNameGroups {
		groups, err := g.processBaseNameGroup(ctx, baseGroup)
		if err != nil {
			log.Ctx(ctx).WithError(err).Warn(fmt.Sprintf("处理基础名称组失败: %s", baseGroup.BaseName))
			continue
		}
		relationGroups = append(relationGroups, groups...)
	}

	return relationGroups, nil
}

// processBaseNameGroup 处理单个基础名称组
func (g *ShowGrouper) processBaseNameGroup(ctx *gin.Context, baseGroup *BaseNameGroup) ([]*RelationGroupInfo, error) {
	switch baseGroup.Strategy {
	case StrategyOrdered:
		return g.processOrderedStrategy(ctx, baseGroup)
	case StrategyDuplicate:
		return g.processDuplicateStrategy(ctx, baseGroup)
	case StrategySpecial:
		return g.processSpecialStrategy(ctx, baseGroup)
	default:
		return nil, fmt.Errorf("未知的处理策略: %v", baseGroup.Strategy)
	}
}

// processOrderedStrategy 处理有序Season策略
func (g *ShowGrouper) processOrderedStrategy(ctx *gin.Context, baseGroup *BaseNameGroup) ([]*RelationGroupInfo, error) {
	// 检查是否有重复的Season需要特殊处理
	var relationGroups []*RelationGroupInfo
	duplicateSeasons := make(map[int][]*ShowInfo)
	orderedShows := make([]*ShowInfo, 0)

	// 分离重复Season和单独Season
	for _, seasonGroup := range baseGroup.SeasonGroups {
		if seasonGroup.Count > 1 && seasonGroup.Season > 0 {
			// 重复Season，需要按字幕数量排序选择最佳的
			duplicateSeasons[seasonGroup.Season] = seasonGroup.Shows
		} else {
			orderedShows = append(orderedShows, seasonGroup.Shows...)
		}
	}

	// 处理重复Season，选择字幕数量最多的
	for season, shows := range duplicateSeasons {
		// 按字幕数量排序
		sort.Slice(shows, func(i, j int) bool {
			return shows[i].EpisodeCount > shows[j].EpisodeCount
		})

		// 选择字幕数量最多的加入有序列表
		orderedShows = append(orderedShows, shows[0])

		// 其余的创建单独的重复处理组
		if len(shows) > 1 {
			duplicateGroup := &RelationGroupInfo{
				Name:         fmt.Sprintf("%s Season %d", baseGroup.BaseName, season),
				Description:  baseGroup.ImdbID,
				RelationType: 2, // 同系列
				Status:       1, // 启用
				Strategy:     StrategyDuplicate,
				ImdbID:       baseGroup.ImdbID,
				Members:      make([]RelationMemberInfo, 0),
			}

			for _, show := range shows[1:] {
				duplicateGroup.Members = append(duplicateGroup.Members, RelationMemberInfo{
					ShowID: show.ID,
					Order:  0, // 重复处理统一为0
					Show:   show,
				})
			}

			if len(duplicateGroup.Members) > 0 {
				relationGroups = append(relationGroups, duplicateGroup)
			}
		}
	}

	// 创建主要的有序关联组
	if len(orderedShows) > 1 {
		mainGroup := &RelationGroupInfo{
			Name:         baseGroup.BaseName,
			Description:  baseGroup.ImdbID,
			RelationType: 2, // 同系列
			Status:       1, // 启用
			Strategy:     StrategyOrdered,
			ImdbID:       baseGroup.ImdbID,
			Members:      make([]RelationMemberInfo, 0),
		}

		// 按Season排序
		sort.Slice(orderedShows, func(i, j int) bool {
			return orderedShows[i].Season < orderedShows[j].Season
		})

		for _, show := range orderedShows {
			order := uint32(show.Season)
			if order == 0 {
				order = 1 // 没有Season信息的设为1
			}

			mainGroup.Members = append(mainGroup.Members, RelationMemberInfo{
				ShowID: show.ID,
				Order:  order,
				Show:   show,
			})
		}

		relationGroups = append(relationGroups, mainGroup)
	}

	return relationGroups, nil
}

// processDuplicateStrategy 处理重复Season策略
func (g *ShowGrouper) processDuplicateStrategy(ctx *gin.Context, baseGroup *BaseNameGroup) ([]*RelationGroupInfo, error) {
	var relationGroups []*RelationGroupInfo

	// 按Season分组处理
	for _, seasonGroup := range baseGroup.SeasonGroups {
		if seasonGroup.Count <= 1 {
			continue // 单个show不需要创建关联组
		}

		groupName := baseGroup.BaseName
		if seasonGroup.Season > 0 {
			groupName = fmt.Sprintf("%s Season %d", baseGroup.BaseName, seasonGroup.Season)
		}

		relationGroup := &RelationGroupInfo{
			Name:         groupName,
			Description:  baseGroup.ImdbID,
			RelationType: 2, // 同系列
			Status:       1, // 启用
			Strategy:     StrategyDuplicate,
			ImdbID:       baseGroup.ImdbID,
			Members:      make([]RelationMemberInfo, 0),
		}

		for _, show := range seasonGroup.Shows {
			relationGroup.Members = append(relationGroup.Members, RelationMemberInfo{
				ShowID: show.ID,
				Order:  0, // 重复处理统一为0
				Show:   show,
			})
		}

		relationGroups = append(relationGroups, relationGroup)
	}

	return relationGroups, nil
}

// processSpecialStrategy 处理特殊标记策略
func (g *ShowGrouper) processSpecialStrategy(ctx *gin.Context, baseGroup *BaseNameGroup) ([]*RelationGroupInfo, error) {
	// 特殊标记按方式2处理，但需要考虑Season信息
	return g.processDuplicateStrategy(ctx, baseGroup)
}

// SaveRelationGroups 保存关联组到数据库
func (g *ShowGrouper) SaveRelationGroups(ctx *gin.Context, relationGroups []*RelationGroupInfo, dryRun bool) (*ProcessStats, error) {
	stats := &ProcessStats{
		CreatedGroups:  0,
		CreatedMembers: 0,
		SkippedGroups:  0,
		ErrorCount:     0,
	}

	if dryRun {
		log.Ctx(ctx).Info("DryRun模式：跳过实际保存操作")
		stats.CreatedGroups = len(relationGroups)
		for _, group := range relationGroups {
			stats.CreatedMembers += len(group.Members)
		}
		return stats, nil
	}

	for _, relationGroup := range relationGroups {
		if len(relationGroup.Members) < 2 {
			log.Ctx(ctx).Warn(fmt.Sprintf("关联组成员数量不足，跳过: %s", relationGroup.Name))
			stats.SkippedGroups++
			continue
		}

		// 创建关联组
		group := &show.ShowRelationGroup{
			Name:         relationGroup.Name,
			Description:  relationGroup.Description,
			RelationType: show.RelationType(relationGroup.RelationType),
			Status:       relationGroup.Status,
		}

		// 创建成员列表
		members := make(show.RelationMemberList, 0, len(relationGroup.Members))
		for _, memberInfo := range relationGroup.Members {
			member := &show.ShowRelationMember{
				ShowID: memberInfo.ShowID,
				Order:  memberInfo.Order,
			}
			members = append(members, member)
		}

		// 使用事务创建关联组和成员
		err := g.showRepo.CreateRelationGroupWithMembers(ctx, group, members)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error(fmt.Sprintf("创建关联组失败: %s", relationGroup.Name))
			stats.ErrorCount++
			continue
		}

		stats.CreatedGroups++
		stats.CreatedMembers += len(relationGroup.Members)

		log.Ctx(ctx).Info(fmt.Sprintf("成功创建关联组: %s (ID: %d, 成员数: %d)",
			relationGroup.Name, group.ID, len(relationGroup.Members)))
	}

	return stats, nil
}
