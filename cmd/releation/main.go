package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"vlab/app/common/dbs"
	"vlab/config"
	"vlab/pkg/helper"
	"vlab/pkg/redis"
)

func main() {
	// 命令行参数定义
	var (
		configPath = flag.String("config", "config/local.ini", "配置文件路径")
		dryRun     = flag.Bool("dry-run", false, "仅分析不执行，显示将要创建的关联关系")
		imdbIDs    = flag.String("imdb-id", "", "处理特定的imdb_id，用逗号分隔多个")
		limit      = flag.Int("limit", 0, "限制处理的imdb_id数量，0表示处理所有")
		batchSize  = flag.Int("batch-size", 100, "批处理大小")
		verbose    = flag.Bool("verbose", false, "显示详细日志")
		help       = flag.Bool("help", false, "显示帮助信息")
	)
	flag.Parse()

	// 显示帮助信息
	if *help {
		printHelp()
		return
	}

	// 显示启动信息
	fmt.Println("=== VLab 剧集关联关系处理工具 ===")
	fmt.Printf("配置文件: %s\n", *configPath)
	if *dryRun {
		fmt.Println("模式: 分析模式 (dry-run)")
	} else {
		fmt.Println("模式: 执行模式")
	}

	// 初始化配置
	if err := initConfig(*configPath); err != nil {
		log.Fatalf("❌ 初始化配置失败: %v", err)
	}

	// 解析IMDB ID列表
	var imdbIDList []string
	if *imdbIDs != "" {
		imdbIDList = parseImdbIDs(*imdbIDs)
		fmt.Printf("指定处理的IMDB ID: %v\n", imdbIDList)
	}

	// 创建处理配置
	processConfig := &ProcessConfig{
		ConfigPath: *configPath,
		DryRun:     *dryRun,
		ImdbIDs:    imdbIDList,
		Limit:      *limit,
		BatchSize:  *batchSize,
		Verbose:    *verbose,
	}

	// 显示处理配置
	printProcessConfig(processConfig)

	// 创建处理器并执行
	processor := NewRelationProcessor(processConfig)
	if err := processor.Process(); err != nil {
		log.Fatalf("❌ 处理失败: %v", err)
	}
}

// initConfig 初始化配置
func initConfig(configPath string) error {
	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 初始化配置
	config.SetupWithPath(configPath)

	// 验证数据库配置
	if config.DbCfg.Host == "" {
		return fmt.Errorf("数据库配置不完整，请检查配置文件")
	}

	// 初始化数据库连接
	dbs.Setup()

	// 初始化Redis连接
	redis.Setup()

	// 初始化其他组件
	helper.Setup()

	fmt.Printf("数据库配置:\n")
	fmt.Printf("  Host: %s:%d\n", config.DbCfg.Host, config.DbCfg.Port)
	fmt.Printf("  Database: %s\n", config.DbCfg.Database)
	fmt.Printf("  Username: %s\n", config.DbCfg.Username)

	return nil
}

// parseImdbIDs 解析IMDB ID列表
func parseImdbIDs(imdbIDsStr string) []string {
	if imdbIDsStr == "" {
		return nil
	}

	parts := strings.Split(imdbIDsStr, ",")
	imdbIDs := make([]string, 0, len(parts))
	
	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			imdbIDs = append(imdbIDs, trimmed)
		}
	}

	return imdbIDs
}

// printProcessConfig 打印处理配置
func printProcessConfig(config *ProcessConfig) {
	fmt.Printf("处理配置:\n")
	if len(config.ImdbIDs) > 0 {
		fmt.Printf("  指定IMDB ID: %d个\n", len(config.ImdbIDs))
	} else {
		fmt.Printf("  处理模式: 自动查找重复IMDB ID\n")
	}
	if config.Limit > 0 {
		fmt.Printf("  处理限制: %d\n", config.Limit)
	}
	fmt.Printf("  批处理大小: %d\n", config.BatchSize)
	fmt.Printf("  详细日志: %v\n", config.Verbose)
	fmt.Printf("  DryRun模式: %v\n", config.DryRun)
	fmt.Println()
}

// printHelp 打印帮助信息
func printHelp() {
	fmt.Println("VLab 剧集关联关系处理工具")
	fmt.Println()
	fmt.Println("用途:")
	fmt.Println("  根据IMDB ID映射关系，自动创建剧集的关联组和成员关系。")
	fmt.Println("  支持按Season顺序处理、重复Season处理和特殊标记处理等多种策略。")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  ./releation [选项]")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  --config string     配置文件路径 (默认: config/local.ini)")
	fmt.Println("  --dry-run          仅分析不执行，显示将要创建的关联关系")
	fmt.Println("  --imdb-id string   处理特定的imdb_id，用逗号分隔多个")
	fmt.Println("  --limit int        限制处理的imdb_id数量，0表示处理所有 (默认: 0)")
	fmt.Println("  --batch-size int   批处理大小 (默认: 100)")
	fmt.Println("  --verbose          显示详细日志")
	fmt.Println("  --help             显示此帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 分析模式，查看将要创建的关联关系")
	fmt.Println("  ./releation --dry-run --verbose")
	fmt.Println()
	fmt.Println("  # 处理特定的IMDB ID")
	fmt.Println("  ./releation --imdb-id tt0145487,tt0372784")
	fmt.Println()
	fmt.Println("  # 限制处理前10个重复的IMDB ID")
	fmt.Println("  ./releation --limit 10")
	fmt.Println()
	fmt.Println("  # 使用自定义配置文件")
	fmt.Println("  ./releation --config config/prod.ini")
	fmt.Println()
	fmt.Println("处理策略:")
	fmt.Println("  1. 有序Season: 检测到不同Season号(1,2,3...)时，按顺序设置order字段")
	fmt.Println("  2. 重复Season: 相同Season的多个记录，order统一为0")
	fmt.Println("  3. 特殊标记: name中包含[]的记录，按重复Season策略处理")
	fmt.Println()
	fmt.Println("注意事项:")
	fmt.Println("  - 建议先使用 --dry-run 模式查看分析结果")
	fmt.Println("  - 处理大量数据时建议使用 --limit 参数分批处理")
	fmt.Println("  - 确保数据库连接配置正确")
}
