package main

import (
	"context"
	"fmt"
	"time"

	"vlab/app/common/dbs"
	show "vlab/app/dao/content_show"
	external_ids "vlab/app/dao/content_show_external_ids"
	"vlab/pkg/log"
	"vlab/pkg/util/ctxUtil"

	"github.com/gin-gonic/gin"
)

// RelationProcessor 关联关系处理器
type RelationProcessor struct {
	showRepo        show.Repo
	externalIdsRepo external_ids.Repo
	analyzer        *NameAnalyzer
	grouper         *ShowGrouper
	config          *ProcessConfig
}

// NewRelationProcessor 创建关联关系处理器
func NewRelationProcessor(config *ProcessConfig) *RelationProcessor {
	return &RelationProcessor{
		showRepo:        show.GetRepo(),
		externalIdsRepo: external_ids.GetRepo(),
		analyzer:        NewNameAnalyzer(),
		grouper:         NewShowGrouper(),
		config:          config,
	}
}

// Process 执行主要处理流程
func (p *RelationProcessor) Process() error {
	// 创建gin context
	ctx := &gin.Context{}
	ctx.Set("request_id", ctxUtil.NewRequestID(context.Background()))

	stats := &ProcessStats{
		StartTime: time.Now(),
	}

	log.Ctx(ctx).Info("=== VLab 剧集关联关系处理工具 ===")
	log.Ctx(ctx).Info(fmt.Sprintf("配置文件: %s", p.config.ConfigPath))
	if p.config.DryRun {
		log.Ctx(ctx).Info("模式: 分析模式 (dry-run)")
	} else {
		log.Ctx(ctx).Info("模式: 执行模式")
	}

	// 1. 获取需要处理的IMDB ID列表
	imdbIDs, err := p.getImdbIDsToProcess(ctx)
	if err != nil {
		return fmt.Errorf("获取IMDB ID列表失败: %w", err)
	}

	if len(imdbIDs) == 0 {
		log.Ctx(ctx).Info("没有找到需要处理的IMDB ID")
		return nil
	}

	log.Ctx(ctx).Info(fmt.Sprintf("找到 %d 个需要处理的IMDB ID", len(imdbIDs)))

	// 2. 处理每个IMDB ID
	totalGroups := 0
	totalMembers := 0

	for i, imdbID := range imdbIDs {
		if p.config.Limit > 0 && i >= p.config.Limit {
			log.Ctx(ctx).Info(fmt.Sprintf("达到处理限制 %d，停止处理", p.config.Limit))
			break
		}

		log.Ctx(ctx).Info(fmt.Sprintf("处理 IMDB ID: %s (%d/%d)", imdbID, i+1, len(imdbIDs)))

		groupStats, err := p.processImdbID(ctx, imdbID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error(fmt.Sprintf("处理IMDB ID %s 失败", imdbID))
			stats.ErrorCount++
			continue
		}

		stats.ProcessedImdbIDs++
		totalGroups += groupStats.CreatedGroups
		totalMembers += groupStats.CreatedMembers
		stats.SkippedGroups += groupStats.SkippedGroups
		stats.ErrorCount += groupStats.ErrorCount

		if p.config.Verbose {
			log.Ctx(ctx).Info(fmt.Sprintf("  创建关联组: %d, 成员关系: %d",
				groupStats.CreatedGroups, groupStats.CreatedMembers))
		}
	}

	// 3. 输出统计信息
	stats.EndTime = time.Now()
	stats.Duration = stats.EndTime.Sub(stats.StartTime)
	stats.CreatedGroups = totalGroups
	stats.CreatedMembers = totalMembers

	p.printStats(ctx, stats)

	return nil
}

// getImdbIDsToProcess 获取需要处理的IMDB ID列表
func (p *RelationProcessor) getImdbIDsToProcess(ctx *gin.Context) ([]string, error) {
	// 如果指定了特定的IMDB ID，直接返回
	if len(p.config.ImdbIDs) > 0 {
		return p.config.ImdbIDs, nil
	}

	// 查询重复的IMDB ID
	log.Ctx(ctx).Info("正在查询重复的IMDB映射关系...")

	duplicateImdbIDs, err := p.findDuplicateImdbIDs(ctx)
	if err != nil {
		return nil, fmt.Errorf("查询重复IMDB ID失败: %w", err)
	}

	return duplicateImdbIDs, nil
}

// findDuplicateImdbIDs 查找重复的IMDB ID
func (p *RelationProcessor) findDuplicateImdbIDs(ctx *gin.Context) ([]string, error) {
	// 执行SQL查询：SELECT imdb_id, COUNT(*) FROM content_show_external_ids WHERE is_match = 2 GROUP BY imdb_id HAVING COUNT(*) > 1

	// 由于GORM不直接支持复杂的GROUP BY HAVING查询，我们使用原生SQL
	type DuplicateResult struct {
		ImdbID string `json:"imdb_id"`
		Count  int    `json:"count"`
	}

	var results []DuplicateResult
	sql := `SELECT imdb_id, COUNT(*) as count
			FROM content_show_external_ids
			WHERE is_match = 2 AND imdb_id IS NOT NULL AND imdb_id != ''
			GROUP BY imdb_id
			HAVING COUNT(*) > 1
			ORDER BY count DESC`

	// 通过数据库连接执行原生SQL
	err := dbs.GetDB().Raw(sql).Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("执行重复IMDB ID查询失败: %w", err)
	}

	imdbIDs := make([]string, 0, len(results))
	for _, result := range results {
		imdbIDs = append(imdbIDs, result.ImdbID)
		if p.config.Verbose {
			log.Ctx(ctx).Info(fmt.Sprintf("  IMDB ID: %s, 重复数量: %d", result.ImdbID, result.Count))
		}
	}

	return imdbIDs, nil
}

// processImdbID 处理单个IMDB ID
func (p *RelationProcessor) processImdbID(ctx *gin.Context, imdbID string) (*ProcessStats, error) {
	// 1. 获取该IMDB ID对应的所有show
	shows, err := p.getShowsByImdbID(ctx, imdbID)
	if err != nil {
		return nil, fmt.Errorf("获取剧集信息失败: %w", err)
	}

	if len(shows) < 2 {
		log.Ctx(ctx).Warn(fmt.Sprintf("IMDB ID %s 对应的剧集数量不足 (%d)，跳过处理", imdbID, len(shows)))
		return &ProcessStats{}, nil
	}

	if p.config.Verbose {
		log.Ctx(ctx).Info(fmt.Sprintf("  找到 %d 个相关剧集", len(shows)))
		for _, show := range shows {
			log.Ctx(ctx).Info(fmt.Sprintf("    - %s (ID: %d)", show.Name, show.ID))
		}
	}

	// 2. 分析剧集名称，进行分组
	baseNameGroups, err := p.analyzer.AnalyzeShows(ctx, shows, imdbID)
	if err != nil {
		return nil, fmt.Errorf("分析剧集名称失败: %w", err)
	}

	if p.config.Verbose {
		for _, group := range baseNameGroups {
			log.Ctx(ctx).Info(fmt.Sprintf("  基础名称: %s, 策略: %s, 剧集数: %d",
				group.BaseName, group.Strategy.String(), len(group.Shows)))
		}
	}

	// 3. 创建关联组
	relationGroups, err := p.grouper.CreateRelationGroups(ctx, baseNameGroups)
	if err != nil {
		return nil, fmt.Errorf("创建关联组失败: %w", err)
	}

	if p.config.Verbose {
		for _, group := range relationGroups {
			log.Ctx(ctx).Info(fmt.Sprintf("  将创建关联组: \"%s\" (%s, 成员数: %d)",
				group.Name, group.Strategy.String(), len(group.Members)))
		}
	}

	// 4. 保存关联组
	stats, err := p.grouper.SaveRelationGroups(ctx, relationGroups, p.config.DryRun)
	if err != nil {
		return nil, fmt.Errorf("保存关联组失败: %w", err)
	}

	return stats, nil
}

// getShowsByImdbID 根据IMDB ID获取剧集信息
func (p *RelationProcessor) getShowsByImdbID(ctx *gin.Context, imdbID string) ([]*ShowInfo, error) {
	// 1. 从external_ids表获取show_id列表
	isMatch := uint8(2)
	filter := &external_ids.Filter{
		ImdbID:  imdbID,
		IsMatch: &isMatch, // 有效匹配
	}

	externalIds, err := p.externalIdsRepo.FindByFilter(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("查询外部ID映射失败: %w", err)
	}

	if len(externalIds) == 0 {
		return nil, fmt.Errorf("未找到IMDB ID %s 对应的映射关系", imdbID)
	}

	// 2. 提取show_id列表
	showIDs := make([]uint64, 0, len(externalIds))
	for _, extID := range externalIds {
		showIDs = append(showIDs, extID.ShowID)
	}

	// 3. 获取show详细信息
	showFilter := &show.Filter{
		IDs:    showIDs,
		Status: 1, // 启用状态
	}

	shows, err := p.showRepo.FindByFilter(ctx, showFilter)
	if err != nil {
		return nil, fmt.Errorf("查询剧集信息失败: %w", err)
	}

	// 4. 转换为ShowInfo结构
	showInfos := make([]*ShowInfo, 0, len(shows))
	for _, s := range shows {
		var createdAt time.Time
		if s.CreatedAt != nil {
			createdAt = time.Time(*s.CreatedAt)
		}
		showInfo := &ShowInfo{
			ID:        s.ID,
			Name:      s.Name,
			Status:    s.Status,
			ImdbID:    imdbID,
			CreatedAt: createdAt,
		}
		showInfos = append(showInfos, showInfo)
	}

	return showInfos, nil
}

// printStats 打印统计信息
func (p *RelationProcessor) printStats(ctx *gin.Context, stats *ProcessStats) {
	log.Ctx(ctx).Info("\n处理完成统计:")
	log.Ctx(ctx).Info(fmt.Sprintf("  处理的IMDB ID: %d", stats.ProcessedImdbIDs))
	log.Ctx(ctx).Info(fmt.Sprintf("  创建的关联组: %d", stats.CreatedGroups))
	log.Ctx(ctx).Info(fmt.Sprintf("  创建的成员关系: %d", stats.CreatedMembers))
	if stats.SkippedGroups > 0 {
		log.Ctx(ctx).Info(fmt.Sprintf("  跳过的关联组: %d", stats.SkippedGroups))
	}
	if stats.ErrorCount > 0 {
		log.Ctx(ctx).Warn(fmt.Sprintf("  错误数量: %d", stats.ErrorCount))
	}
	log.Ctx(ctx).Info(fmt.Sprintf("  耗时: %v", stats.Duration.Round(time.Millisecond)))

	if p.config.DryRun {
		log.Ctx(ctx).Info("📝 这是DryRun模式，没有实际创建数据")
	} else {
		log.Ctx(ctx).Info("✅ 处理完成")
	}
}
