package main

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	episode "vlab/app/dao/content_episode"
	subtitle "vlab/app/dao/content_subtitle"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
)

// NameAnalyzer 名称分析器
type NameAnalyzer struct {
	episodeRepo  episode.Repo
	subtitleRepo subtitle.Repo
}

// NewNameAnalyzer 创建名称分析器
func NewNameAnalyzer() *NameAnalyzer {
	return &NameAnalyzer{
		episodeRepo:  episode.GetRepo(),
		subtitleRepo: subtitle.GetRepo(),
	}
}

// AnalyzeShows 分析剧集列表，返回分组信息
func (a *NameAnalyzer) AnalyzeShows(ctx *gin.Context, shows []*ShowInfo, imdbID string) ([]*BaseNameGroup, error) {
	if len(shows) == 0 {
		return nil, nil
	}

	// 1. 解析每个show的名称信息
	for _, show := range shows {
		if err := a.parseShowName(show); err != nil {
			log.Ctx(ctx).WithError(err).Warn(fmt.Sprintf("解析剧集名称失败: %s", show.Name))
		}
	}

	// 2. 按基础名称分组
	baseNameGroups := a.groupByBaseName(shows, imdbID)

	// 3. 为每个基础名称组确定处理策略
	for _, group := range baseNameGroups {
		if err := a.determineStrategy(ctx, group); err != nil {
			log.Ctx(ctx).WithError(err).Warn(fmt.Sprintf("确定处理策略失败: %s", group.BaseName))
		}
	}

	return baseNameGroups, nil
}

// parseShowName 解析剧集名称
func (a *NameAnalyzer) parseShowName(show *ShowInfo) error {
	name := strings.TrimSpace(show.Name)
	if name == "" {
		return fmt.Errorf("剧集名称为空")
	}

	// 检查是否有特殊标记 []
	specialRegex := regexp.MustCompile(`\[([^\]]+)\]`)
	if matches := specialRegex.FindStringSubmatch(name); len(matches) > 1 {
		show.HasSpecial = true
		show.SpecialTag = matches[1]
		// 移除特殊标记
		name = specialRegex.ReplaceAllString(name, "")
		name = strings.TrimSpace(name)
	}

	// 解析Season信息
	seasonRegex := regexp.MustCompile(`^(.+?)\s+Season\s+(\d+)$`)
	if matches := seasonRegex.FindStringSubmatch(name); len(matches) == 3 {
		show.BaseName = strings.TrimSpace(matches[1])
		if season, err := strconv.Atoi(matches[2]); err == nil {
			show.Season = season
		}
	} else {
		// 没有Season信息，整个名称作为基础名称
		show.BaseName = name
		show.Season = 0
	}

	return nil
}

// groupByBaseName 按基础名称分组
func (a *NameAnalyzer) groupByBaseName(shows []*ShowInfo, imdbID string) []*BaseNameGroup {
	groupMap := make(map[string]*BaseNameGroup)

	for _, show := range shows {
		baseName := show.BaseName
		if baseName == "" {
			baseName = show.Name // 如果没有解析出基础名称，使用原名称
		}

		group, exists := groupMap[baseName]
		if !exists {
			group = &BaseNameGroup{
				BaseName: baseName,
				Shows:    make([]*ShowInfo, 0),
				ImdbID:   imdbID,
			}
			groupMap[baseName] = group
		}

		group.Shows = append(group.Shows, show)
		if show.HasSpecial {
			group.HasSpecial = true
		}
	}

	// 转换为切片
	groups := make([]*BaseNameGroup, 0, len(groupMap))
	for _, group := range groupMap {
		groups = append(groups, group)
	}

	return groups
}

// determineStrategy 确定处理策略
func (a *NameAnalyzer) determineStrategy(ctx *gin.Context, group *BaseNameGroup) error {
	// 按Season分组
	seasonMap := make(map[int][]*ShowInfo)
	hasSeasonInfo := false

	for _, show := range group.Shows {
		if show.Season > 0 {
			hasSeasonInfo = true
		}
		seasonMap[show.Season] = append(seasonMap[show.Season], show)
	}

	// 创建季度分组
	group.SeasonGroups = make([]*SeasonGroup, 0)
	for season, shows := range seasonMap {
		seasonGroup := &SeasonGroup{
			Season: season,
			Shows:  shows,
			Count:  len(shows),
		}
		group.SeasonGroups = append(group.SeasonGroups, seasonGroup)
	}

	// 确定策略
	if group.HasSpecial {
		group.Strategy = StrategySpecial
	} else if hasSeasonInfo && a.hasDuplicateSeasons(seasonMap) {
		group.Strategy = StrategyDuplicate
		// 为重复的Season获取集数统计
		if err := a.getEpisodeCountsForDuplicates(ctx, group); err != nil {
			log.Ctx(ctx).WithError(err).Warn("获取集数统计失败")
		}
	} else if hasSeasonInfo {
		group.Strategy = StrategyOrdered
	} else {
		group.Strategy = StrategyDuplicate // 没有Season信息，按重复处理
	}

	return nil
}

// hasDuplicateSeasons 检查是否有重复的Season
func (a *NameAnalyzer) hasDuplicateSeasons(seasonMap map[int][]*ShowInfo) bool {
	for season, shows := range seasonMap {
		if season > 0 && len(shows) > 1 {
			return true
		}
	}
	return false
}

// getEpisodeCountsForDuplicates 为重复Season获取集数统计
func (a *NameAnalyzer) getEpisodeCountsForDuplicates(ctx *gin.Context, group *BaseNameGroup) error {
	for _, seasonGroup := range group.SeasonGroups {
		if seasonGroup.Count <= 1 {
			continue
		}

		// 为每个show获取集数统计
		for _, show := range seasonGroup.Shows {
			count, err := a.getEpisodeSubtitleCount(ctx, show.ID)
			if err != nil {
				log.Ctx(ctx).WithError(err).Warn(fmt.Sprintf("获取剧集 %d 的集数统计失败", show.ID))
				continue
			}
			show.EpisodeCount = count
		}
	}

	return nil
}

// getEpisodeSubtitleCount 获取剧集的字幕数量统计
func (a *NameAnalyzer) getEpisodeSubtitleCount(ctx *gin.Context, showID uint64) (int, error) {
	// 1. 获取该show的所有episode
	episodeFilter := &episode.Filter{
		ShowID: showID,
	}
	episodes, err := a.episodeRepo.FindByFilter(ctx, episodeFilter)
	if err != nil {
		return 0, fmt.Errorf("查询episode失败: %w", err)
	}

	if len(episodes) == 0 {
		return 0, nil
	}

	// 2. 获取第一个episode的ID
	firstEpisodeID := episodes[0].ID

	// 3. 统计该episode的字幕数量
	subtitleFilter := &subtitle.Filter{
		EpisodeID: firstEpisodeID,
	}
	count, err := a.subtitleRepo.CountByFilter(ctx, subtitleFilter)
	if err != nil {
		return 0, fmt.Errorf("统计字幕数量失败: %w", err)
	}

	return int(count), nil
}
