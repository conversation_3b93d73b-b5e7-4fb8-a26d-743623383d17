package main

import (
	"time"
)

// ShowInfo 剧集信息结构
type ShowInfo struct {
	ID           uint64    `json:"id"`
	Name         string    `json:"name"`
	Status       uint32    `json:"status"`
	BaseName     string    `json:"base_name"`     // 基础名称，如"Sex and the City"
	Season       int       `json:"season"`        // 季度号，如1,2,3
	HasSpecial   bool      `json:"has_special"`   // 是否有特殊标记
	SpecialTag   string    `json:"special_tag"`   // 特殊标记内容，如"Audio Latino"
	EpisodeCount int       `json:"episode_count"` // 集数统计（用于重复Season的排序）
	ImdbID       string    `json:"imdb_id"`       // IMDB ID
	CreatedAt    time.Time `json:"created_at"`
}

// GroupStrategy 分组策略
type GroupStrategy int

const (
	StrategyOrdered   GroupStrategy = 1 // 按Season顺序处理
	StrategyDuplicate GroupStrategy = 2 // 重复Season处理
	StrategySpecial   GroupStrategy = 3 // 特殊标记处理
)

func (s GroupStrategy) String() string {
	switch s {
	case StrategyOrdered:
		return "有序Season"
	case StrategyDuplicate:
		return "重复Season处理"
	case StrategySpecial:
		return "特殊标记处理"
	default:
		return "未知策略"
	}
}

// RelationGroupInfo 关联组信息
type RelationGroupInfo struct {
	Name         string              `json:"name"`
	Description  string              `json:"description"`
	RelationType uint32              `json:"relation_type"` // 2-同系列
	Status       uint32              `json:"status"`        // 1-启用
	Members      []RelationMemberInfo `json:"members"`
	Strategy     GroupStrategy       `json:"strategy"`
	ImdbID       string              `json:"imdb_id"`
}

// RelationMemberInfo 关联组成员信息
type RelationMemberInfo struct {
	ShowID uint64 `json:"show_id"`
	Order  uint32 `json:"order"`
	Show   *ShowInfo `json:"show,omitempty"` // 关联的剧集信息
}

// ProcessStats 处理统计信息
type ProcessStats struct {
	ProcessedImdbIDs    int           `json:"processed_imdb_ids"`
	CreatedGroups       int           `json:"created_groups"`
	CreatedMembers      int           `json:"created_members"`
	SkippedGroups       int           `json:"skipped_groups"`
	ErrorCount          int           `json:"error_count"`
	StartTime           time.Time     `json:"start_time"`
	EndTime             time.Time     `json:"end_time"`
	Duration            time.Duration `json:"duration"`
}

// ProcessConfig 处理配置
type ProcessConfig struct {
	ConfigPath   string   `json:"config_path"`
	DryRun       bool     `json:"dry_run"`
	ImdbIDs      []string `json:"imdb_ids"`      // 指定处理的IMDB ID列表
	Limit        int      `json:"limit"`         // 限制处理数量
	BatchSize    int      `json:"batch_size"`    // 批处理大小
	Verbose      bool     `json:"verbose"`       // 详细日志
}

// SeasonGroup 季度分组信息
type SeasonGroup struct {
	Season int         `json:"season"`
	Shows  []*ShowInfo `json:"shows"`
	Count  int         `json:"count"`
}

// BaseNameGroup 基础名称分组
type BaseNameGroup struct {
	BaseName     string         `json:"base_name"`
	Shows        []*ShowInfo    `json:"shows"`
	SeasonGroups []*SeasonGroup `json:"season_groups"`
	Strategy     GroupStrategy  `json:"strategy"`
	HasSpecial   bool           `json:"has_special"`
	ImdbID       string         `json:"imdb_id"`
}
