# VLab 剧集关联关系处理工具

## 概述

这是一个用于处理VLab平台剧集关联关系的命令行工具。该工具能够自动识别具有相同IMDB ID的重复剧集，并根据剧集名称的规律创建合适的关联组和成员关系。

## 功能特性

- **自动识别重复映射**: 查找`content_show_external_ids`表中重复的IMDB ID映射关系
- **智能名称分析**: 解析剧集名称中的Season信息和特殊标记
- **多种处理策略**: 支持有序Season、重复Season和特殊标记等处理策略
- **安全的DryRun模式**: 支持仅分析不执行的模式，便于预览结果
- **灵活的参数配置**: 支持指定特定IMDB ID、限制处理数量等
- **详细的日志输出**: 提供详细的处理过程和统计信息

## 处理策略

### 策略1: 有序Season处理
当检测到不同的Season号（如Season 1, Season 2, Season 3...）时：
- 创建关联组，`relation_type=2`（同系列）
- 按Season顺序设置`order`字段
- 如果出现重复Season，通过episode数量排序选择最佳的

### 策略2: 重复Season处理
当检测到相同Season的多个记录时：
- 创建关联组，`relation_type=2`（同系列）
- 所有成员的`order`字段统一设为0

### 策略3: 特殊标记处理
当检测到name中包含`[]`的记录时：
- 按策略2处理，但会识别特殊标记内容

## 安装和编译

### 方式1: 使用Makefile（推荐）

```bash
# 进入工具目录
cd cmd/releation

# 查看可用命令
make usage

# 编译程序
make build

# 运行测试
make test

# 查看帮助
make help
```

### 方式2: 直接使用Go命令

```bash
# 进入项目根目录
cd /path/to/vlab_releation

# 编译工具
go build -o bin/releation cmd/releation/*.go

# 或者直接运行
go run cmd/releation/*.go [选项]
```

## 使用方法

### 基本用法

```bash
# 查看帮助信息
./bin/releation --help

# 分析模式（推荐首次使用）
./bin/releation --dry-run --verbose

# 执行模式
./bin/releation
```

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--config` | string | `config/local.ini` | 配置文件路径 |
| `--dry-run` | bool | false | 仅分析不执行，显示将要创建的关联关系 |
| `--imdb-id` | string | - | 处理特定的imdb_id，用逗号分隔多个 |
| `--limit` | int | 0 | 限制处理的imdb_id数量，0表示处理所有 |
| `--batch-size` | int | 100 | 批处理大小 |
| `--verbose` | bool | false | 显示详细日志 |
| `--help` | bool | false | 显示帮助信息 |

### 使用示例

```bash
# 1. 分析模式，查看将要创建的关联关系
./bin/releation --dry-run --verbose

# 2. 处理特定的IMDB ID
./bin/releation --imdb-id tt0145487,tt0372784

# 3. 限制处理前10个重复的IMDB ID
./bin/releation --limit 10

# 4. 使用生产环境配置文件
./bin/releation --config config/prd.ini

# 5. 详细模式处理所有重复IMDB ID
./bin/releation --verbose
```

## 输出示例

```
=== VLab 剧集关联关系处理工具 ===
配置文件: config/local.ini
模式: 分析模式 (dry-run)

正在查询重复的IMDB映射关系...
找到 15 个重复的IMDB ID

处理 IMDB ID: tt0145487 (1/15)
  找到 8 个相关剧集
  基础名称: Sex and the City, 策略: 有序Season, 剧集数: 8
  将创建关联组: "Sex and the City" (有序Season, 成员数: 5)
  将创建关联组: "Sex and the City Season 1" (重复Season处理, 成员数: 3)

处理完成统计:
  处理的IMDB ID: 15
  创建的关联组: 12
  创建的成员关系: 156
  跳过的关联组: 2
  耗时: 2.3s
📝 这是DryRun模式，没有实际创建数据
```

## 数据库表结构

工具操作以下数据库表：

### 输入表
- `content_show_external_ids`: 剧集外部ID映射表
- `content_show`: 剧集基本信息表
- `content_episode`: 剧集集数表
- `content_subtitle`: 字幕表

### 输出表
- `content_show_relation_group`: 剧集关联组表
- `content_show_relation_member`: 剧集关联组成员表

## 注意事项

1. **建议先使用DryRun模式**: 首次使用时建议加上`--dry-run`参数，查看分析结果后再执行
2. **分批处理大量数据**: 处理大量数据时建议使用`--limit`参数分批处理
3. **确保数据库连接**: 确保配置文件中的数据库连接信息正确
4. **备份数据**: 执行前建议备份相关数据表
5. **监控日志**: 注意观察处理过程中的错误和警告信息

## 错误处理

工具具有完善的错误处理机制：
- 单个IMDB ID处理失败不会影响其他ID的处理
- 详细的错误日志帮助定位问题
- 统计信息包含错误计数

## 技术架构

```
cmd/releation/
├── main.go           # 主入口，命令行参数处理
├── processor.go      # RelationProcessor核心处理器
├── analyzer.go       # NameAnalyzer名称分析器
├── grouper.go        # ShowGrouper分组处理器
├── types.go          # 数据结构定义
└── README.md         # 使用说明文档
```

## 依赖项

- Go 1.18+
- GORM v2
- Gin框架
- VLab项目的DAO层和配置系统

## 贡献

如需修改或扩展功能，请遵循项目的代码规范和架构设计。
