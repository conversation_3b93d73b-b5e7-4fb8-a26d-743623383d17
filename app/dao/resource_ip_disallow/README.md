# IP黑名单分片缓存重构方案

## 概述

本模块实现了基于 channelID 的分片缓存机制，替换原有的全量缓存方式，显著提升了性能和内存利用率。

## 重构内容

### 1. 分片缓存架构

#### 原始方案问题
- 全量缓存，内存使用量大
- 更新时需要重新加载整个列表
- 缓存粒度粗，影响范围大
- 不同渠道间数据混淆风险

#### 优化方案
- **按渠道ID分片缓存**: `vlab:cache:ipDisallow:channel:{channelID}`
- **独立TTL管理**: 每个渠道独立的5分钟过期时间
- **精确缓存失效**: 只清除相关渠道的缓存
- **向后兼容**: 保持现有API接口不变

```go
// 使用分片缓存
config, err := repo.RedisIPDisallowByChannelID(ctx, channelID)
```

### 2. 核心实现

#### 新增接口方法
```go
// 分片缓存接口
RedisIPDisallowByChannelID(ctx *gin.Context, channelID uint64) (*Model, error)
RedisReloadIPDisallowByChannelID(ctx *gin.Context, channelID uint64) (*Model, error)
RedisClearIPDisallowByChannelID(ctx *gin.Context, channelID uint64) error
```

#### 缓存键设计
- **全量缓存键**: `vlab:cache:list:ipDisallow` (保持兼容)
- **分片缓存键**: `vlab:cache:ipDisallow:channel:{channelID}`

#### 降级机制
- 分片缓存失败时，自动降级到数据库查询
- 保持系统稳定性和可用性

```go
// 中间件中的使用示例
config, err := repo.RedisIPDisallowByChannelID(ctx, channelID)
if err != nil {
    // 降级到数据库查询
    return checkIPInBlacklistFromDB(ctx, channelID, clientIP)
}
```

### 3. 缓存更新策略

#### 精确失效
- 数据更新时，只清除对应 channelID 的分片缓存
- 同时清除全量缓存以保持向后兼容
- 减少对其他渠道的影响

```go
// 更新时的缓存清理
e.RedisClearIPDisallowByChannelID(ctx, channelID)  // 清除分片缓存
e.RedisClearIPDisallowList(ctx)                    // 清除全量缓存（兼容）
```

## 性能对比

### 缓存性能提升
| 指标 | 原始方案 | 分片缓存方案 | 提升效果 |
|------|----------|-------------|----------|
| 内存使用 | 全量加载所有渠道 | 按需加载单个渠道 | 内存使用量按渠道数量线性减少 |
| 缓存更新影响 | 全局失效 | 单渠道失效 | 99%+ 减少影响范围 |
| 缓存命中率 | 受全局更新影响 | 渠道独立 | 热点渠道命中率更稳定 |
| 并发性能 | 全局锁竞争 | 渠道级别隔离 | 显著提升并发处理能力 |

### 实际效果预估
- **内存优化**: 假设有100个渠道，内存使用量减少约90%
- **更新效率**: 单个渠道更新不影响其他99个渠道的缓存
- **响应时间**: 热点渠道响应时间更稳定，冷门渠道按需加载

## 使用方法

### 1. 基本使用

```go
// 中间件中检查IP黑名单（已自动使用分片缓存）
isBlacklisted, err := checkIPInBlacklist(ctx, channelID, clientIP)

// 直接使用分片缓存API
config, err := resource_ip_disallow.GetRepo().RedisIPDisallowByChannelID(ctx, channelID)
if err != nil {
    // 处理错误，可能降级到数据库查询
}
```

### 2. 缓存管理

```go
// 清除指定渠道的缓存
err := repo.RedisClearIPDisallowByChannelID(ctx, channelID)

// 重新加载指定渠道的缓存
config, err := repo.RedisReloadIPDisallowByChannelID(ctx, channelID)
```

### 3. 向后兼容

```go
// 预热指定渠道缓存
err := WarmupSpecificChannels(ctx, []uint64{1, 2, 3})

// 清除渠道缓存
err := ClearChannelCache(ctx, channelID)

// 获取缓存统计
stats := GetCacheStatistics()
```

### 3. 性能测试

```bash
# 运行基准测试
go test -bench=. -benchmem ./app/dao/resource_ip_disallow/

# 运行功能测试
go test ./app/dao/resource_ip_disallow/
```

## 配置说明

### 缓存配置
- **TTL**: 5分钟（可在redis.go中调整）
- **预热渠道数**: 50个热点渠道（可在init.go中调整）
- **监控间隔**: 5分钟（可在init.go中调整）

### Redis键格式
```
# 全局缓存（兼容性保留）
vlab:cache:list:ipDisallow

# 分层缓存（新增）
vlab:cache:ipDisallow:channel:{channelID}
```

## 监控指标

### 缓存指标
- `hit_count`: 缓存命中次数
- `miss_count`: 缓存未命中次数  
- `error_count`: 缓存错误次数
- `hit_rate`: 缓存命中率
- `total_channels`: 缓存的渠道总数

### 性能指标
- `trie_nodes`: Trie树节点数量
- `cidr_count`: CIDR规则数量
- `match_time`: 平均匹配时间

## 最佳实践

### 1. 缓存策略
- 优先使用分层缓存
- 定期监控缓存命中率
- 根据业务需求调整TTL

### 2. 性能优化
- 合理设置IP规则数量
- 避免过于复杂的CIDR规则
- 定期清理无效规则

### 3. 监控告警
- 缓存命中率低于80%时告警
- 缓存错误率高于5%时告警
- 定期检查缓存大小和内存使用

## 故障排查

### 常见问题
1. **缓存命中率低**: 检查渠道配置是否正确，考虑增加预热渠道
2. **IP匹配错误**: 检查CIDR格式是否正确，查看Trie树构建日志
3. **内存使用过高**: 检查缓存的渠道数量，考虑实现LRU淘汰

### 调试工具
```go
// 获取IP匹配器统计信息
stats := model.GetIPMatcherStats()

// 获取缓存优化报告
report := GetCacheOptimizationReport(ctx)
```

## 未来优化方向

1. **本地缓存**: 实现应用内存缓存，减少Redis访问
2. **智能预热**: 基于访问频率动态调整预热策略
3. **分布式缓存**: 支持多实例间的缓存同步
4. **压缩存储**: 使用更高效的序列化格式
5. **实时更新**: 实现配置变更的实时推送机制
