package resource_ip_disallow

import (
	"testing"
	redisPkg "vlab/pkg/redis"

	"github.com/stretchr/testify/assert"
)

// TestCacheKeyGeneration 测试缓存键生成
func TestCacheKeyGeneration(t *testing.T) {
	testCases := []struct {
		channelID uint64
		expected  string
	}{
		{1, "vlab:cache:ipDisallow:channel:1"},
		{123, "vlab:cache:ipDisallow:channel:123"},
		{999999, "vlab:cache:ipDisallow:channel:999999"},
	}

	for _, tc := range testCases {
		result := redisPkg.GetIPDisallowChannelKey(tc.channelID)
		assert.Equal(t, tc.expected, result)
	}
}

// BenchmarkIPBlacklistCheck 性能基准测试
func BenchmarkIPBlacklistCheck(b *testing.B) {
	// 创建测试模型
	model := &Model{
		ChannelID: 1,
		IPList:    "***********/24,10.0.0.0/8,**********/12",
		Status:    1,
	}

	testIPs := []string{
		"*************",
		"*********",
		"**********",
		"*******", // 不在黑名单中
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, ip := range testIPs {
			model.IsIPInBlacklist(ip)
		}
	}
}
