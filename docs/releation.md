```sql
-- auto-generated definition  
create table content_show_external_ids  
(  
    id           bigint unsigned auto_increment comment '主键ID'  
        primary key,  
    show_id      bigint unsigned                            not null comment '关联content_show表的ID',  
    trakt_id     int unsigned                               null comment 'Trakt平台ID',  
    slug         varchar(255)                               null comment 'Slug标识符（如：spider-man-2002）',  
    imdb_id      varchar(20)                                null comment 'IMDB ID（如：tt0145487）',  
    tmdb_id      int unsigned                               null comment 'TMDB（The Movie Database）ID',  
    match_type   varchar(10)                                null comment '匹配类型：movie-电影，show-电视剧',  
    match_score  decimal(5, 2)                              null comment '匹配分数（0-100），90分以上为有效匹配',  
    match_reason text                                       null comment '匹配理由说明',  
    is_match     tinyint(1)       default 0                 not null comment '是否有效匹配：2-是，1-否',  
    source       varchar(50)      default 'imdb_api'        null comment '数据来源：imdb_api-IMDB API，manual-手动录入',  
    raw_response json                                       null comment '原始API响应数据（JSON格式）',  
    created_at   datetime         default CURRENT_TIMESTAMP null comment '创建时间',  
    updated_at   datetime         default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',  
    is_deleted   tinyint unsigned default 0                 null comment '软删除标记：0-正常，1-已删除',  
    constraint uk_show_id  
        unique (show_id, is_deleted) comment '确保每个剧集只有一条有效记录'  
)  
    comment '剧集外部ID映射表：存储VLab剧集与IMDB、TMDB、Trakt等外部平台的ID映射关系，用于数据同步和内容匹配'  
    collate = utf8mb4_unicode_ci;  
  
create index idx_created_at  
    on content_show_external_ids (created_at)  
    comment '创建时间索引';  
  
create index idx_imdb_id  
    on content_show_external_ids (imdb_id)  
    comment 'IMDB ID索引';  
  
create index idx_is_deleted  
    on content_show_external_ids (is_deleted)  
    comment '软删除索引';  
  
create index idx_match_type  
    on content_show_external_ids (match_type)  
    comment '匹配类型索引';  
  
create index idx_show_id_is_deleted  
    on content_show_external_ids (show_id, is_deleted)  
    comment '联合索引优化查询';  
  
create index idx_slug  
    on content_show_external_ids (slug)  
    comment 'Slug索引';  
  
create index idx_tmdb_id  
    on content_show_external_ids (tmdb_id)  
    comment 'TMDB ID索引';  
  
create index idx_trakt_id  
    on content_show_external_ids (trakt_id)  
    comment 'Trakt ID索引';
```

以上为一张关联表
`SELECT imdb_id, COUNT(*) FROM content_show_external_ids WHERE is_match = 2 GROUP BY imdb_id HAVING COUNT(*) > 1;`
这句 SQL 列出了关联超过 1 个以上的 imdb 映射关系
现在以 imdb_id 为 X 的数据，COUNT (*) 假设为 10 个，

`SELECT * FROM content_show_external_ids WHERE is_match = 2 AND imdb_id = X;`
使用这句 SQL 去获取不同的 show_id 列表，继续去 `SELECT * FROM content_show WHERE id IN (a, b, c, d, e);`

```sql
-- auto-generated definition  
create table content_show  
(  
    id                int(11) unsigned auto_increment  
        primary key,  
    status            tinyint             default 0                 not null,  
    name              varchar(64)         default ''                not null comment '节目名称',  
    name_key          varchar(64)         default ''                not null comment '名称唯一键',  
    overview          text                                          null comment '简介',  
    overview_key      varchar(64)         default ''                not null comment '简介唯一键',  
    langs             varchar(64)                                   null,  
    air_date          varchar(20)                                   null comment '首播日期',  
    air_date_ts       timestamp           default CURRENT_TIMESTAMP not null comment '首播日期',  
    air_date_key      varchar(64)         default ''                not null comment '首播日期',  
    content_type      int                 default 0                 not null comment '内容类型',  
    in_production     tinyint             default 0                 not null comment '是否在制作',  
    score             int                 default 0                 not null comment '评分',  
    presentation_time int                 default 0                 not null,  
    franchise_id      int                 default 0                 not null,  
    is_deleted        tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',  
    created_at        timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',  
    updated_at        timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',  
    homepage          varchar(255)        default ''                not null  
)  
    comment '节目表';  
  
create index idx_air_date  
    on content_show (air_date_ts);  
  
create index idx_created  
    on content_show (created_at);  
  
create index idx_score  
    on content_show (score);  
  
create index idx_status_isdeleted  
    on content_show (status, is_deleted, presentation_time);  
  
create index show_idx_date_key  
    on content_show (air_date_key);  
  
create index show_idx_franchise  
    on content_show (franchise_id);  
  
create index show_idx_name_key  
    on content_show (name_key);  
  
create index show_idx_overview_key  
    on content_show (overview_key);
```


识别 name 形如 [
  {
    "name": "VWXYZ Season 2"
  },
  {
    "name": "VWXYZ Season 1"
  }
]
VWXYZ 为代指，
1. 这种有 Season 1 2 3 4 5 的区别，将这种记录为管理组名称为 VWXYZ，描述为 imdb_id，关联关系为 2，状态为启用，并分别记录到 member 表，按照 season 顺序记录 `order` 字段
	a. 出现多个 season a 的情况，需要从 `SELECT * FROM content_episode WHERE show_id IN (?);` 中获取第一个 episode 的 ID，继而去 `SELECT episode_id, count(*) as cnt FROM content_subtitle WHERE episode_id IN (?) GROUP BY episode_id ORDER BY cnt DESC;`, cnt 多的 show_id 参与到方式 1 的处理中
		 
2. 若出现有相同的 VWXYZ Season a（a 为代指），则将这些记录为管理组名称为 VWXYZ，描述为 imdb_id，关联关系为启用，并分别记录到 member 表，`order` 字段统一为 0
3. 若出现 name 中带有[]的，则直接按方式 2 处理

举例：
```json
[
  {
    "id": 12983,
    "status": 1,
    "name": "Sex and the City Season 1"
  },
  {
    "id": 18596,
    "status": 1,
    "name": "Sex and the City Season 1"
  },
  {
    "id": 18649,
    "status": 1,
    "name": "Sex and the City Season 1"
  },
  {
    "id": 15749,
    "status": 1,
    "name": "Sex and the City Season 3"
  },
  {
    "id": 18592,
    "status": 1,
    "name": "Sex and the City Season 3"
  },
  {
    "id": 18656,
    "status": 1,
    "name": "Sex and the City Season 4[Audio Latino]"
  },
  {
    "id": 12981,
    "status": 1,
    "name": "Sex and the City Season 5"
  },
  {
    "id": 18617,
    "status": 1,
    "name": "Sex and the City Season 6"
  }
]
```
以上结构中，出现了三个 season 1，则需要按方式 2 处理为一类，出现了Sex and the City Season 4[Audio Latino]这种[]，则需要按方式 3 处理，同时因为有明确的 Season 顺序，所以需要使用方式 1 来处理

```sql
create table content_show_relation_group  
(  
    id            bigint unsigned auto_increment comment '主键ID'  
        primary key,  
    name          varchar(255)                               null comment '关联组名称',  
    description   text                                       null comment '关联组描述',  
    relation_type tinyint unsigned default 1                 null comment '关联类型: 1-相关推荐,2-同系列,3-相似题材',  
    status        tinyint unsigned default 1                 null comment '状态: 1-启用,2-禁用',  
    created_at    datetime         default CURRENT_TIMESTAMP null comment '创建时间',  
    updated_at    datetime         default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',  
    is_deleted    tinyint unsigned default 0                 null comment '是否删除: 0-否,1-是'  
)  
    comment '剧集关联组表' collate = utf8mb4_unicode_ci;  
  
create index idx_name  
    on content_show_relation_group (name);  
  
create index idx_relation_type  
    on content_show_relation_group (relation_type);  
  
create index idx_status  
    on content_show_relation_group (status);
    
    
-- auto-generated definition  
create table content_show_relation_member  
(  
    id         bigint unsigned auto_increment comment '主键ID'  
        primary key,  
    group_id   bigint unsigned                            not null comment '关联组ID',  
    show_id    bigint unsigned                            not null comment '剧ID',  
    `order`    int unsigned     default 0                 null comment '在组内的排序',  
    created_at datetime         default CURRENT_TIMESTAMP null comment '创建时间',  
    updated_at datetime         default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',  
    is_deleted tinyint unsigned default 0                 null comment '是否删除: 0-否,1-是',  
    constraint uk_group_show  
        unique (group_id, show_id, is_deleted)  
)  
    comment '剧集关联组成员表' collate = utf8mb4_unicode_ci;  
  
create index idx_group_id  
    on content_show_relation_member (group_id);  
  
create index idx_order  
    on content_show_relation_member (`order`);  
  
create index idx_show_id  
    on content_show_relation_member (show_id);
```