[app]
AppName=vlab
Env=test
JwtSecret=WL67c%GEra
JwtTimeout=604800
HttpPort=8081
LogPath=./data/logs
ReadTimeout=60
WriteTimeout=60

[mysql]
Host=127.0.0.1
Port=3306
Database=vlab
Username=root
Password=admin888

[mysql_read]
Host=127.0.0.1
Port=3306
Database=vlab
Username=root
Password=admin888

[redis]
Host=127.0.0.1:6379
Password=
Db=2
MaxIdle=8
MaxActive=32
IdleTimeout=300
Prefix=

[qcloud]
AccessKey="qadkbSapPuO1hG9wFaihl1_86MIGQP87hFORxoGy"
SecretKey="opha2CGe3vRtT2YsA8VZPeJ76hpCf9MmO2eIItSx"
Bucket=mowantoy

[kafka]
Version="2.7.2"

[kafka.producer]
#Address=***********:9092,***********:9092,**********:9092

[kafka.consumer]
#Assignor="roundrobin"
#Brokers=***********:9092,***********:9092,**********:9092
#Group="wsd"
#Topics=1,2
#Oldest=true


[elasticsearch]
#Host=http://************:9200,http://************:9200,http://***********:9200

[vikingdb]
Host=api-vikingdb.volces.com
Region=cn-beijing
AccessKeyID=your-access-key-id
AccessKeySecret=your-access-key-secret
Scheme=https
ConnectionTimeout=300
Collection=vlab-shows
Index=vlab-shows-index
ModelName=doubao-embedding-240715
UseSparse=true
DenseWeight=0.5
BatchSize=20
MaxRetries=3
RetryDelay=1000
# 文档模式配置
DocumentMode=merged
TextMergeStrategy=weighted
TitleWeight=2.0
# 定时同步配置
SyncWindowHours=8
EnableAutoSync=false
# 异步上传配置
UseAsyncUpload=true
# 上传超时配置
UploadTimeout=600

